import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useChat } from "@/hooks/useChat";
import { useAuth } from "@/contexts/AuthContext";
import { chatService } from "@/services/chatService";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronDown, RefreshCw, Bug, CheckCircle, XCircle, AlertTriangle } from "lucide-react";

export const ChatDebugger: React.FC = () => {
  const { chats, isLoading, error, loadChats } = useChat();
  const { token, isAuthenticated, user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [rawApiResponse, setRawApiResponse] = useState<any>(null);
  const [isOpen, setIsOpen] = useState(false);

  const runDiagnostics = async () => {
    console.log('🔍 Running chat diagnostics...');
    
    const diagnostics = {
      timestamp: new Date().toISOString(),
      authentication: {
        isAuthenticated,
        hasToken: !!token,
        tokenLength: token?.length || 0,
        tokenPrefix: token?.substring(0, 20) + '...',
        user: user ? { id: user.id, name: user.name, email: user.email } : null
      },
      chatContext: {
        chatsCount: chats?.length || 0,
        isLoading,
        error,
        chatsData: chats
      },
      apiTest: null as any
    };

    // Test direct API call
    try {
      console.log('🌐 Testing direct API call...');
      const cleanToken = token?.replace('Bearer ', '');
      const apiResponse = await chatService.getChats(cleanToken);
      
      diagnostics.apiTest = {
        success: apiResponse.isSuccess,
        status: apiResponse.status,
        error: apiResponse.error,
        dataExists: !!apiResponse.data,
        dataType: typeof apiResponse.data,
        rawResponse: apiResponse
      };
      
      setRawApiResponse(apiResponse);
      console.log('📡 API Response:', apiResponse);
    } catch (error) {
      diagnostics.apiTest = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        rawResponse: null
      };
      console.error('❌ API Test failed:', error);
    }

    setDebugInfo(diagnostics);
    console.log('📊 Diagnostics complete:', diagnostics);
  };

  useEffect(() => {
    runDiagnostics();
  }, [chats, isLoading, error, token, isAuthenticated]);

  const getStatusIcon = (condition: boolean) => {
    return condition ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={condition ? "default" : "destructive"} className="ml-2">
        {condition ? trueText : falseText}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Chat System Debugger
          <Button 
            onClick={runDiagnostics} 
            size="sm" 
            variant="outline"
            className="ml-auto"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {debugInfo && (
          <>
            {/* Authentication Status */}
            <div className="space-y-2">
              <h3 className="font-semibold flex items-center gap-2">
                {getStatusIcon(debugInfo.authentication.isAuthenticated)}
                Authentication Status
              </h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Authenticated: {getStatusBadge(debugInfo.authentication.isAuthenticated, "Yes", "No")}</div>
                <div>Has Token: {getStatusBadge(debugInfo.authentication.hasToken, "Yes", "No")}</div>
                <div>Token Length: <Badge variant="outline">{debugInfo.authentication.tokenLength}</Badge></div>
                <div>User: <Badge variant="outline">{debugInfo.authentication.user?.name || "None"}</Badge></div>
              </div>
            </div>

            {/* Chat Context Status */}
            <div className="space-y-2">
              <h3 className="font-semibold flex items-center gap-2">
                {getStatusIcon(debugInfo.chatContext.chatsCount > 0)}
                Chat Context Status
              </h3>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Chats Count: <Badge variant="outline">{debugInfo.chatContext.chatsCount}</Badge></div>
                <div>Loading: {getStatusBadge(!debugInfo.chatContext.isLoading, "No", "Yes")}</div>
                <div>Error: {getStatusBadge(!debugInfo.chatContext.error, "None", debugInfo.chatContext.error || "Unknown")}</div>
              </div>
            </div>

            {/* API Test Results */}
            <div className="space-y-2">
              <h3 className="font-semibold flex items-center gap-2">
                {debugInfo.apiTest ? getStatusIcon(debugInfo.apiTest.success) : <AlertTriangle className="h-4 w-4 text-yellow-500" />}
                API Test Results
              </h3>
              {debugInfo.apiTest && (
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Success: {getStatusBadge(debugInfo.apiTest.success, "Yes", "No")}</div>
                  <div>Status: <Badge variant="outline">{debugInfo.apiTest.status}</Badge></div>
                  <div>Data Exists: {getStatusBadge(debugInfo.apiTest.dataExists, "Yes", "No")}</div>
                  <div>Data Type: <Badge variant="outline">{debugInfo.apiTest.dataType}</Badge></div>
                  {debugInfo.apiTest.error && (
                    <div className="col-span-2">
                      Error: <Badge variant="destructive">{debugInfo.apiTest.error}</Badge>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Raw Data Inspection */}
            <Collapsible open={isOpen} onOpenChange={setIsOpen}>
              <CollapsibleTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  Raw Data Inspection
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-4 mt-4">
                {/* Chat Context Data */}
                <div>
                  <h4 className="font-medium mb-2">Chat Context Data:</h4>
                  <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                    {JSON.stringify(debugInfo.chatContext.chatsData, null, 2)}
                  </pre>
                </div>

                {/* Raw API Response */}
                {rawApiResponse && (
                  <div>
                    <h4 className="font-medium mb-2">Raw API Response:</h4>
                    <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(rawApiResponse, null, 2)}
                    </pre>
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>

            {/* Quick Actions */}
            <div className="flex gap-2">
              <Button onClick={loadChats} size="sm">
                Reload Chats
              </Button>
              <Button 
                onClick={() => console.log('Current chats:', chats)} 
                size="sm" 
                variant="outline"
              >
                Log Chats to Console
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
