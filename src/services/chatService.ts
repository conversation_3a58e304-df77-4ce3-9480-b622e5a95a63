import { ApiResponse, apiService } from './api';
import {
  <PERSON><PERSON>,
  ChatListResponse,
  MessagesResponse,
  SendMessageRequest,
  SendMessageResponse
} from '@/types/chat';

/**
 * Chat Service
 * Handles all chat-related API operations using the existing apiService
 */
export const chatService = {
  /**
   * Get list of chats for the current user
   * @param token - Authorization token
   * @returns Promise with chat list response
   */
  getChats: async (token?: string): Promise<ApiResponse<ChatListResponse>> => {
    console.log('🔍 ChatService.getChats called with token:', token ? 'Present' : 'Missing');

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
      console.log('🔑 Authorization header set');
    }

    // Define interface for raw API response
    interface RawChatResponse {
      id: string;
      type: 'direct' | 'group';
      created_at: string;
      updated_at: string;
      participants: Array<{
        id: number;
        name: string;
        email: string;
      }>;
      name?: string;
      description?: string;
    }

    console.log('📡 Making API call to /api/chats with headers:', headers);

    const response = await apiService<RawChatResponse[]>('/api/chats', {
      method: 'GET',
      headers,
      requiresAuth: true,
      includeCredentials: true,
    });

    console.log('📨 API Response received:', {
      isSuccess: response.isSuccess,
      status: response.status,
      error: response.error,
      dataType: typeof response.data,
      dataLength: Array.isArray(response.data) ? response.data.length : 'Not array',
      rawData: response.data
    });

    if (response.isSuccess && response.data && Array.isArray(response.data)) {
      console.log('✅ Response successful, transforming data...');
      try {
        // Transform the raw API response into the expected ChatListResponse format
        const transformedChats: Chat[] = response.data.map(chat => {
          if (!chat.id || !chat.type || !chat.created_at || !chat.updated_at) {
            throw new Error(`Invalid chat data: missing required fields for chat ${chat.id}`);
          }

          return {
            id: chat.id,
            type: chat.type,
            name: chat.name || chat.participants[0]?.name || 'Unknown',
            description: chat.description,
            created_at: chat.created_at,
            updated_at: chat.updated_at,
            participants: Array.isArray(chat.participants)
              ? chat.participants.map((participant) => ({
                  id: participant.id.toString(),
                  name: participant.name,
                  email: participant.email,
                  type: 'customer' as const
                }))
              : [],
            last_message: undefined,
            unread_count: 0
          };
        });

        console.log('🔄 Data transformation complete:', {
          originalCount: response.data.length,
          transformedCount: transformedChats.length,
          transformedChats
        });

        return {
          ...response,
          data: {
            data: transformedChats,
            meta: {
              current_page: 1,
              last_page: 1,
              per_page: transformedChats.length,
              total: transformedChats.length
            }
          }
        };
      } catch (error) {
        console.error('❌ Data transformation failed:', error);
        return {
          data: null,
          error: error instanceof Error ? error.message : 'Failed to transform chat data',
          status: response.status,
          isSuccess: false
        };
      }
    }

    console.error('❌ API call failed or invalid response:', {
      isSuccess: response.isSuccess,
      error: response.error,
      status: response.status,
      dataExists: !!response.data,
      isArray: Array.isArray(response.data)
    });

    return {
      data: null,
      error: response.error || 'Invalid response format',
      status: response.status,
      isSuccess: false
    };
  },

  /**
   * Get messages for a specific chat
   * @param chatId - Chat ID
   * @param page - Page number for pagination (optional)
   * @param token - Authorization token
   * @returns Promise with messages response
   */
  getMessages: async (
    chatId: string, 
    page: number = 1, 
    token?: string
  ): Promise<ApiResponse<MessagesResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    let endpoint = `/api/chats/${chatId}/messages`;
    if (page > 1) {
      endpoint += `?page=${page}`;
    }

    return apiService<MessagesResponse>(endpoint, {
      method: 'GET',
      headers,
      requiresAuth: true,
      includeCredentials: true,
    });
  },

  /**
   * Send a message to a chat
   * @param chatId - Chat ID
   * @param messageData - Message content and type
   * @param token - Authorization token
   * @returns Promise with sent message response
   */
  sendMessage: async (
    chatId: string, 
    messageData: SendMessageRequest, 
    token?: string
  ): Promise<ApiResponse<SendMessageResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<SendMessageResponse>(`/api/chats/${chatId}/messages`, {
      method: 'POST',
      headers,
      body: messageData,
      requiresAuth: true,
      includeCredentials: true,
    });
  },

  /**
   * Mark all messages in a chat as read
   * @param chatId - Chat ID
   * @param token - Authorization token
   * @returns Promise with success response
   */
  markAsRead: async (
    chatId: string, 
    token?: string
  ): Promise<ApiResponse<{ success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ success: boolean; message?: string }>(`/api/chats/${chatId}/read`, {
      method: 'POST',
      headers,
      requiresAuth: true,
      includeCredentials: true,
    });
  },

  /**
   * Delete a specific message
   * @param chatId - Chat ID
   * @param messageId - Message ID
   * @param token - Authorization token
   * @returns Promise with success response
   */
  deleteMessage: async (
    chatId: string, 
    messageId: string, 
    token?: string
  ): Promise<ApiResponse<{ success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ success: boolean; message?: string }>(
      `/api/chats/${chatId}/messages/${messageId}`,
      {
        method: 'DELETE',
        headers,
        requiresAuth: true,
        includeCredentials: true,
      }
    );
  },

  /**
   * Create a new chat (if needed for admin functionality)
   * @param userId - User ID to include in chat
   * @param type - Chat type ('direct' or 'group')
   * @param name - Chat name (optional, for group chats)
   * @param token - Authorization token
   * @returns Promise with created chat response
   */
  createChat: async (
    userId: string,
    type: 'direct' | 'group' = 'direct',
    name?: string,
    token?: string
  ): Promise<ApiResponse<{ data: Chat; success: boolean; message?: string }>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return apiService<{ data: Chat; success: boolean; message?: string }>('/api/chats', {
      method: 'POST',
      headers,
      body: { user_id: userId, type, name },
      requiresAuth: true,
      includeCredentials: true,
    });
  },
};
